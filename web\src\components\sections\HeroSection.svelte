<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { ArrowUpRight, Play, CheckCircle, Bot } from 'lucide-svelte';

  let streamingJobs = $state([] as any[]); // will become [job1,job2,…job40,job1,job2,…job40]
  let isLoading = $state(true);

  // Fetch jobs from database with reduced limit for faster loading
  async function fetchJobs() {
    try {
      console.log('Fetching jobs from API...');
      const response = await fetch('/api/jobs?limit=200&random=true');
      console.log('Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API response data:', data);
      console.log('Jobs count:', data.jobs?.length || 0);

      return data.jobs || [];
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      return [];
    }
  }

  const statusStages = [
    { status: 'scanning', color: 'blue', text: 'Scanning', icon: '🔍' },
    { status: 'matching', color: 'yellow', text: 'Matching', icon: '🎯' },
    { status: 'applying', color: 'orange', text: 'Applying', icon: '📝' },
    { status: 'applied', color: 'green', text: 'Applied', icon: '✅' },
  ];

  function getJobStatus(jobAge: number) {
    if (jobAge < 5) return statusStages[0]; // scanning
    if (jobAge < 10) return statusStages[1]; // matching
    if (jobAge < 15) return statusStages[2]; // applying
    return statusStages[3]; // applied
  }

  function transformJobForDisplay(job: any, index: number) {
    const company = job?.company || 'Unknown Company';
    const companyInitial = company.charAt(0).toUpperCase();
    const logoColors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-red-500',
      'bg-yellow-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500',
    ];
    const logoColor = logoColors[company.length % logoColors.length];

    // Extract industry with fallback
    let industry = 'Technology';
    if (job?.industryTags?.length) {
      industry = job.industryTags[0];
    } else if (job?.industry) {
      industry = job.industry;
    } else {
      const titleLower = (job?.title || '').toLowerCase();
      if (/(health|medical|nurse|doctor)/.test(titleLower)) {
        industry = 'Healthcare';
      } else if (/(finance|bank|accounting)/.test(titleLower)) {
        industry = 'Finance';
      } else if (/(market|brand|social media)/.test(titleLower)) {
        industry = 'Marketing';
      } else if (/(sales|account manager|business development)/.test(titleLower)) {
        industry = 'Sales';
      } else if (/(design|ui|ux|graphic)/.test(titleLower)) {
        industry = 'Design';
      } else if (/(engineer|developer|programmer|software)/.test(titleLower)) {
        industry = 'Technology';
      } else if (/(teacher|education|instructor)/.test(titleLower)) {
        industry = 'Education';
      } else if (/(operations|logistics|supply chain)/.test(titleLower)) {
        industry = 'Operations';
      } else if (/(consultant|advisor)/.test(titleLower)) {
        industry = 'Consulting';
      }
    }

    // Extract seniority with fallback
    let seniority = 'Mid-Level';
    if (job?.experienceLevel) {
      seniority = job.experienceLevel;
    } else if (job?.seniorityLevel) {
      seniority = job.seniorityLevel;
    } else {
      const titleLower = (job?.title || '').toLowerCase();
      if (/(senior|sr\.|lead)/.test(titleLower)) {
        seniority = 'Senior Level';
      } else if (/(junior|jr\.|entry|associate)/.test(titleLower)) {
        seniority = 'Entry Level';
      } else if (/(principal|staff)/.test(titleLower)) {
        seniority = 'Principal';
      } else if (/(director|head of|vp|vice president)/.test(titleLower)) {
        seniority = 'Director';
      } else if (/(manager|lead)/.test(titleLower)) {
        seniority = 'Lead';
      }
    }

    const benefits = Array.isArray(job?.benefits) ? job.benefits.slice(0, 3) : [];

    return {
      id: job?.id || Math.random().toString(),
      uniqueKey: `${job?.id || 'job'}_${index}_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`,
      company,
      role: job?.title || 'Software Engineer',
      location: job?.location || 'Remote',
      salary: job?.salary || 'Competitive',
      companyInitial,
      logoColor,
      matchScore: Math.floor(Math.random() * 30) + 70, // random 70–99%
      industry,
      seniority,
      benefits,
      description: job?.description ? job.description.slice(0, 100) + '...' : '',
      age: Math.floor(Math.random() * 20),
    };
  }

  // On client‐only, fetch once and initialize a **doubled** list
  if (typeof window !== 'undefined') {
    (async function initializeStream() {
      isLoading = true;
      const jobs = await fetchJobs();
      if (jobs.length > 0) {
        // Pick the first 40 (or whatever you need to fill the screen)
        const initialCount = 40;
        const baseSlice = jobs.slice(0, initialCount);

        // Transform those 40 into display objects
        const firstBatch = baseSlice.map((job: any, i: number) => transformJobForDisplay(job, i));
        // Then concat an identical copy so that we have 80 total items
        streamingJobs = [...firstBatch, ...firstBatch];

        isLoading = false;
      } else {
        console.warn('No jobs found in database');
        isLoading = false;
      }
    })();

    // Every 1.5s, randomize some ages to flicker statuses (just to keep icons moving)
    setInterval(() => {
      if (!streamingJobs.length) return;
      for (let i = 0; i < streamingJobs.length; i++) {
        if (Math.random() < 0.2) {
          streamingJobs[i].age = Math.floor(Math.random() * 20);
        }
      }
      // Trigger Svelte reactivity
      streamingJobs = streamingJobs;
    }, 1500);
  }
</script>

<style>
  /* Float effect unused in this demo, but kept for reference */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }

  /*
    Instead of scrolling the full −100%, we only scroll −50%.
    Because we've duplicated the list (first 40 + second 40),
    once the first 40 slide up out of view, the second 40 are in the exact same positions,
    and the animation can loop seamlessly.
  */
  @keyframes scroll-up {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-50%);
    }
  }

  /*
    Make sure this element is tall enough to hold both copies.
    We no longer need to explicitly set `height:` in JS;
    the content's natural height (2× the single list) will do.
  */
  .animate-scroll-up {
    animation: scroll-up 40s linear infinite;
  }
</style>

<section class="-mt-17 -z-50">
  <div class="grid lg:grid-cols-[2fr_4fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative flex flex-col justify-center overflow-hidden">
      <!-- Main content -->
      <div class="mt-17 relative z-10 max-w-md space-y-8 p-10">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700">
            <Bot class="h-4 w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-6xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs Automatically
          </h1>

          <p class="text-lg text-gray-600">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              href="/auth/sign-up"
              class="group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Get Started
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <Button
              variant="ghost"
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </Button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-1">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>•</span>
            <span>Setup in minutes</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="bg-accent relative h-[900px] overflow-hidden">
      <!-- Main content area -->
      <div class="relative flex flex-col justify-center">
        <!-- Endless Vertical Scrolling Stream -->
        <div class="h-full overflow-hidden p-4">
          {#if isLoading}
            <!-- Loading state -->
            <div class="flex h-full items-center justify-center">
              <div class="text-center">
                <div
                  class="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600">
                </div>
                <p class="text-gray-600">Loading jobs from database...</p>
              </div>
            </div>
          {:else if streamingJobs.length === 0}
            <!-- No jobs state -->
            <div class="flex h-full items-center justify-center">
              <div class="text-center">
                <p class="text-gray-600">No jobs found in database</p>
              </div>
            </div>
          {:else}
            <!--
              NOTE: We removed the inline style="height:" entirely,
              because the container's height will just be whatever the rendered content is.
            -->
            <div class="animate-scroll-up columns-2 gap-3 md:columns-3 lg:columns-4">
              {#each streamingJobs as job (job.uniqueKey)}
                {@const currentStatus = getJobStatus(job.age || 0)}
                <Card.Root
                  class="mb-2 break-inside-avoid gap-0 p-0 transition-all duration-500 ease-in-out"
                  style="animation-delay: {job.age * 100}ms">
                  <Card.Header class="border-border border-b !p-2">
                    <div class="flex items-start gap-3">
                      <div class="h-8 w-8 overflow-hidden rounded-lg p-1">
                        <div
                          class="flex h-full w-full items-center justify-center {job.logoColor} text-xs font-bold text-white">
                          {job.companyInitial}
                        </div>
                      </div>
                      <div class="min-w-0 flex-1">
                        <div class="text-sm font-semibold text-gray-900">{job.role}</div>
                        <div class="text-xs text-gray-600">{job.company}</div>
                        <div class="mt-1 flex flex-wrap gap-1 text-xs">
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.industry}</span>
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.seniority}</span>
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                          <div>{job.location} • {job.salary}</div>
                        </div>
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content class="!p-2">
                    <!-- Optional description for varying heights -->
                    {#if job.description}
                      <div class="mt-2 text-xs text-gray-600">
                        {job.description}
                      </div>
                    {/if}

                    <!-- Optional benefits for varying heights -->
                    {#if job.benefits && job.benefits.length > 0}
                      <div class="mt-2 flex flex-wrap gap-1">
                        {#each job.benefits as benefit}
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700">
                            {benefit}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </Card.Content>
                  <Card.Footer
                    class="border-border flex items-center justify-between border-t !p-2">
                    <div class="flex items-center gap-1.5">
                      <div
                        class="flex h-3 w-3 items-center justify-center rounded-full bg-{currentStatus.color}-100">
                        <span class="text-{currentStatus.color}-600">{currentStatus.icon}</span>
                      </div>
                      <span class="text-xs font-medium text-gray-700">
                        {currentStatus.text}
                      </span>
                    </div>
                    <div
                      class="rounded-full bg-{currentStatus.color}-50 px-2 py-0.5 text-xs font-bold text-{currentStatus.color}-700">
                      {job.matchScore}% match
                    </div>
                  </Card.Footer>
                </Card.Root>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</section>
